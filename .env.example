# ===== SUPABASE CONFIGURATION =====
# Supabase project URL (get from your Supabase project settings)
SUPABASE_URL="https://your-project-id.supabase.co"

# Supabase anonymous key (get from your Supabase project settings)
SUPABASE_ANON_KEY="your-supabase-anon-key"

# ===== AUTHENTICATION & SECURITY =====
# JWT secret key for token signing (use a strong, random string in production)
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# Session secret for Express sessions (use a strong, random string in production)
SESSION_SECRET="your-session-secret-change-this-in-production"

# ===== SERVER CONFIGURATION =====
# Port for the Express server
PORT=3000

# Node environment (development, production, test)
NODE_ENV=development

# ===== FILE UPLOAD CONFIGURATION =====
# Maximum file size for uploads (in bytes)
MAX_FILE_SIZE=5242880

# Allowed file types for image uploads
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp"

# Upload directory path (relative to server root)
UPLOAD_DIR="server/uploads"

# ===== CORS CONFIGURATION =====
# Allowed origins for CORS (comma-separated for multiple origins)
CORS_ORIGIN="http://localhost:3000,http://localhost:6666"

# ===== RATE LIMITING =====
# Rate limit window in minutes
RATE_LIMIT_WINDOW=15

# Maximum requests per window
RATE_LIMIT_MAX_REQUESTS=100

# ===== LOGGING =====
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Log file path (optional, logs to console if not set)
# LOG_FILE="logs/app.log"

# ===== OPTIONAL: EXTERNAL SERVICES =====
# Email service configuration (if implementing email features)
# EMAIL_SERVICE="gmail"
# EMAIL_USER="<EMAIL>"
# EMAIL_PASSWORD="your-app-password"

# Cloud storage configuration (if implementing cloud storage)
# CLOUD_STORAGE_PROVIDER="aws"
# AWS_ACCESS_KEY_ID="your-access-key"
# AWS_SECRET_ACCESS_KEY="your-secret-key"
# AWS_BUCKET_NAME="your-bucket-name"
# AWS_REGION="us-east-1"

# ===== DEVELOPMENT ONLY =====
# Enable debug mode (development only)
DEBUG=true

# Enable hot reload for development
HOT_RELOAD=true

# Database seeding (set to true to seed database on startup)
SEED_DATABASE=false
