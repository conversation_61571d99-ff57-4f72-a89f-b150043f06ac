import { Link } from 'wouter';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/hooks/use-language';
import { Product } from '@shared/schema';
import { Sparkles } from 'lucide-react';

interface HeroProps {
  featuredProduct?: Product;
  isLoading?: boolean;
}

export function Hero({ featuredProduct, isLoading = false }: HeroProps) {
  const { t, language } = useLanguage();

  return (
    <section className="relative min-h-screen flex items-center justify-center pt-20">
      <div className="absolute inset-0 z-0">
        <img
          src="https://images.unsplash.com/photo-1618220179428-22790b461013?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&h=1080"
          alt="Luxury marble background"
          className="w-full h-full object-cover opacity-30"
        />
        <div className="absolute inset-0 luxury-gradient opacity-90"></div>
      </div>
      <div className="relative z-10 max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className={`text-center ${language === 'en' ? 'lg:text-left' : 'lg:text-right'}`}>
            <h1 className="font-playfair text-5xl lg:text-7xl font-bold text-charcoal leading-tight mb-6">
              {t('hero.title')}
              <span className="block text-gold">{t('hero.subtitle')}</span>
            </h1>
            <p className="text-xl text-medium-gray leading-relaxed mb-8 max-w-2xl">{t('hero.description')}</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link href="/products">
                <Button
                  size="lg"
                  className="px-8 py-4 gold-gradient text-white font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                  <Sparkles className="mr-2 h-5 w-5" />
                  {t('hero.exploreCollection')}
                </Button>
              </Link>
              <Link href="#categories">
                <Button
                  variant="outline"
                  size="lg"
                  className="px-8 py-4 border-2 border-charcoal text-charcoal hover:bg-charcoal hover:text-white transition-all duration-300">
                  {t('hero.viewCategories')}
                </Button>
              </Link>
            </div>
          </div>

          {/* Featured Product Showcase */}
          <div className="relative">
            {isLoading ? (
              <Card className="elegant-shadow rounded-2xl overflow-hidden bg-white p-6">
                <div className="animate-pulse">
                  <div className="bg-gray-200 h-80 rounded-xl mb-4"></div>
                  <div className="space-y-3">
                    <div className="flex justify-between items-start">
                      <div className="bg-gray-200 h-6 w-3/4 rounded"></div>
                      <div className="bg-gray-200 h-6 w-16 rounded"></div>
                    </div>
                    <div className="bg-gray-200 h-4 w-full rounded"></div>
                    <div className="bg-gray-200 h-4 w-2/3 rounded"></div>
                    <div className="bg-gray-200 h-10 w-full rounded"></div>
                  </div>
                </div>
              </Card>
            ) : featuredProduct ? (
              <Card className="elegant-shadow rounded-2xl overflow-hidden bg-white p-6">
                <img
                  src={
                    featuredProduct.images?.[0] ||
                    'https://images.unsplash.com/photo-1615880484746-a134be9a6ecf?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600'
                  }
                  alt={language === 'ar' && featuredProduct.name_ar ? featuredProduct.name_ar : featuredProduct.name}
                  className="w-full h-80 object-cover rounded-xl mb-4"
                />
                <CardContent className="space-y-3 p-0">
                  <div className="flex justify-between items-start">
                    <h3 className="font-playfair text-2xl font-semibold text-charcoal">
                      {language === 'ar' && featuredProduct.name_ar ? featuredProduct.name_ar : featuredProduct.name}
                    </h3>
                    <Badge className="px-3 py-1 bg-gold text-white font-medium">{featuredProduct.code}</Badge>
                  </div>
                  <p className="text-medium-gray leading-relaxed">
                    {language === 'ar' && featuredProduct.description_ar
                      ? featuredProduct.description_ar
                      : featuredProduct.description}
                  </p>
                  <Link href={`/products/${featuredProduct.id}`}>
                    <Button
                      variant="outline"
                      className="w-full py-3 border border-gold text-gold hover:bg-gold hover:text-white transition-all duration-300 font-medium">
                      {t('products.viewDetails')}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ) : null}
          </div>
        </div>
      </div>
    </section>
  );
}
