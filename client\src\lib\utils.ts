import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// RTL-aware utility functions
export function rtlSpace(isRTL: boolean, spacing: string) {
  if (spacing.startsWith('space-x-')) {
    const value = spacing.replace('space-x-', '');
    return isRTL ? `space-x-reverse space-x-${value}` : spacing;
  }
  return spacing;
}

export function rtlMargin(isRTL: boolean, leftClass: string, rightClass?: string) {
  if (!rightClass) {
    // Handle cases like 'ml-2' -> 'mr-2' in RTL
    if (leftClass.startsWith('ml-')) {
      const value = leftClass.replace('ml-', '');
      return isRTL ? `mr-${value}` : leftClass;
    }
    if (leftClass.startsWith('mr-')) {
      const value = leftClass.replace('mr-', '');
      return isRTL ? `ml-${value}` : leftClass;
    }
    if (leftClass.startsWith('pl-')) {
      const value = leftClass.replace('pl-', '');
      return isRTL ? `pr-${value}` : leftClass;
    }
    if (leftClass.startsWith('pr-')) {
      const value = leftClass.replace('pr-', '');
      return isRTL ? `pl-${value}` : leftClass;
    }
  }
  return isRTL ? rightClass || leftClass : leftClass;
}

export function rtlFlexDirection(isRTL: boolean, direction: 'row' | 'row-reverse' = 'row') {
  if (direction === 'row') {
    return isRTL ? 'flex-row-reverse' : 'flex-row';
  }
  return isRTL ? 'flex-row' : 'flex-row-reverse';
}

export function rtlTextAlign(isRTL: boolean, align: 'left' | 'right' | 'center' = 'left') {
  if (align === 'center') return 'text-center';
  if (align === 'left') return isRTL ? 'text-right' : 'text-left';
  if (align === 'right') return isRTL ? 'text-left' : 'text-right';
  return align;
}
