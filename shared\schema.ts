import { z } from "zod";

// User schema
export const userSchema = z.object({
  id: z.string().uuid().optional(),
  username: z.string().min(1),
  password: z.string().min(6),
  role: z.string().default("admin"),
  created_at: z.string().optional(),
});

// Category schema
export const categorySchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  name_ar: z.string().optional(),
  slug: z.string().min(1),
  description: z.string().optional(),
  description_ar: z.string().optional(),
  image: z.string().optional(),
  sort_order: z.number().default(0),
  is_active: z.boolean().default(true),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

// Product schema
export const productSchema = z.object({
  id: z.string().uuid().optional(),
  code: z.string().min(1),
  name: z.string().min(1),
  name_ar: z.string().optional(),
  category_id: z.string().uuid(),
  description: z.string().optional(),
  description_ar: z.string().optional(),
  full_description: z.string().optional(),
  full_description_ar: z.string().optional(),
  images: z.array(z.string()).default([]),
  specifications: z.record(z.string()).default({}),
  specifications_ar: z.record(z.string()).default({}),
  applications: z.array(z.string()).default([]),
  applications_ar: z.array(z.string()).default([]),
  material: z.string().optional(),
  origin: z.string().optional(),
  finish: z.string().optional(),
  thickness: z.string().optional(),
  availability: z.string().optional(),
  is_active: z.boolean().default(true),
  is_featured: z.boolean().default(false),
  sort_order: z.number().default(0),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

// Site settings schema
export const siteSettingSchema = z.object({
  id: z.string().uuid().optional(),
  key: z.string().min(1),
  value: z.any(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

// Insert schemas (for API validation)
export const insertUserSchema = userSchema.omit({
  id: true,
  created_at: true,
});

export const insertCategorySchema = categorySchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export const insertProductSchema = productSchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export const insertSiteSettingSchema = siteSettingSchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
});

// Types
export type User = z.infer<typeof userSchema>;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Category = z.infer<typeof categorySchema>;
export type InsertCategory = z.infer<typeof insertCategorySchema>;

export type Product = z.infer<typeof productSchema>;
export type InsertProduct = z.infer<typeof insertProductSchema>;

export type SiteSetting = z.infer<typeof siteSettingSchema>;
export type InsertSiteSetting = z.infer<typeof insertSiteSettingSchema>;
