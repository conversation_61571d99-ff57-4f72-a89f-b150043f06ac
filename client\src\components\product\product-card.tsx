import { Link } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Product, Category } from "@shared/schema";
import { useLanguage } from "@/hooks/use-language";

interface ProductCardProps {
  product: Product;
  category?: Category;
}

export function ProductCard({ product, category }: ProductCardProps) {
  const { t, language } = useLanguage();
  
  const productName = language === 'ar' && product.name_ar ? product.name_ar : product.name;
  const productDescription = language === 'ar' && product.description_ar ? product.description_ar : product.description;
  const categoryName = category
    ? (language === 'ar' && category.name_ar ? category.name_ar : category.name)
    : '';

  const mainImage = product.images?.[0] || 'https://images.unsplash.com/photo-1565043666747-69f6646db940?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&h=400';

  return (
    <Link href={`/products/${product.id}`}>
      <Card className="product-card bg-white rounded-2xl elegant-shadow overflow-hidden cursor-pointer">
        <div className="relative">
          <img 
            src={mainImage} 
            alt={productName}
            className="w-full h-56 object-cover"
            loading="lazy"
          />
          {product.isFeatured && (
            <Badge className="absolute top-4 left-4 bg-gold text-white">
              Featured
            </Badge>
          )}
        </div>
        <CardContent className="p-6">
          <div className="flex justify-between items-start mb-3">
            <h3 className="font-playfair text-xl font-semibold text-charcoal line-clamp-1">
              {productName}
            </h3>
            <Badge 
              variant="outline" 
              className="px-3 py-1 bg-gold text-white border-gold font-medium"
            >
              {product.code}
            </Badge>
          </div>
          {categoryName && (
            <p className="text-medium-gray text-sm mb-4">{categoryName}</p>
          )}
          <p className="text-medium-gray leading-relaxed line-clamp-2">
            {productDescription}
          </p>
        </CardContent>
      </Card>
    </Link>
  );
}
