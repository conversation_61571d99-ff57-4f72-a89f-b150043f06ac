import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check SUPABASE_URL and SUPABASE_ANON_KEY.');
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types for TypeScript
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          username: string;
          password: string;
          role: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          username: string;
          password: string;
          role?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          username?: string;
          password?: string;
          role?: string;
          created_at?: string;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          name_ar: string | null;
          slug: string;
          description: string | null;
          description_ar: string | null;
          image: string | null;
          sort_order: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          name_ar?: string | null;
          slug: string;
          description?: string | null;
          description_ar?: string | null;
          image?: string | null;
          sort_order?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          name_ar?: string | null;
          slug?: string;
          description?: string | null;
          description_ar?: string | null;
          image?: string | null;
          sort_order?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      products: {
        Row: {
          id: string;
          code: string;
          name: string;
          name_ar: string | null;
          category_id: string;
          description: string | null;
          description_ar: string | null;
          full_description: string | null;
          full_description_ar: string | null;
          images: string[];
          specifications: Record<string, string>;
          specifications_ar: Record<string, string>;
          applications: string[];
          applications_ar: string[];
          material: string | null;
          origin: string | null;
          finish: string | null;
          thickness: string | null;
          availability: string | null;
          is_active: boolean;
          is_featured: boolean;
          sort_order: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          code: string;
          name: string;
          name_ar?: string | null;
          category_id: string;
          description?: string | null;
          description_ar?: string | null;
          full_description?: string | null;
          full_description_ar?: string | null;
          images?: string[];
          specifications?: Record<string, string>;
          specifications_ar?: Record<string, string>;
          applications?: string[];
          applications_ar?: string[];
          material?: string | null;
          origin?: string | null;
          finish?: string | null;
          thickness?: string | null;
          availability?: string | null;
          is_active?: boolean;
          is_featured?: boolean;
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          code?: string;
          name?: string;
          name_ar?: string | null;
          category_id?: string;
          description?: string | null;
          description_ar?: string | null;
          full_description?: string | null;
          full_description_ar?: string | null;
          images?: string[];
          specifications?: Record<string, string>;
          specifications_ar?: Record<string, string>;
          applications?: string[];
          applications_ar?: string[];
          material?: string | null;
          origin?: string | null;
          finish?: string | null;
          thickness?: string | null;
          availability?: string | null;
          is_active?: boolean;
          is_featured?: boolean;
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      site_settings: {
        Row: {
          id: string;
          key: string;
          value: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          key: string;
          value: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          key?: string;
          value?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}

// Typed Supabase client
export const typedSupabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
