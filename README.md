# 🏛️ Luxury Stone & Ceramic Digital Showroom

A sophisticated, full-stack web application showcasing premium natural stones, marble, granite, travertine, and ceramic tiles. This high-end digital showroom serves as a professional portfolio to attract luxury clients with its modern design and comprehensive product catalog.

## ✨ Features

- **🎨 Luxury Design**: Modern, clean interface with sophisticated color palette (charcoal, warm white, beige, gold, bronze)
- **🌍 Multi-language Support**: Full English and Arabic support with RTL layout
- **📱 Responsive Design**: Optimized for all devices and screen sizes
- **🔐 Admin Panel**: Secure product and category management system
- **🖼️ Image Management**: Multiple image support with organized storage
- **🎯 Non-E-commerce**: Pure showcase platform focused on visual presentation

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and optimized builds
- **Tailwind CSS** for styling with custom luxury theme
- **Shadcn/ui** component library with Radix UI primitives
- **Wouter** for lightweight client-side routing
- **TanStack Query** for server state management
- **React Hook Form** with Zod validation

### Backend
- **Node.js** with Express.js REST API
- **TypeScript** with ES modules
- **Supabase** for database and authentication
- **PostgreSQL** via Supabase with real-time capabilities
- **JWT Authentication** with bcrypt password hashing
- **Multer** for file upload handling

### Development Tools
- **ESBuild** for production builds
- **PostCSS** with Autoprefixer
- **TypeScript** strict mode
- **Supabase CLI** for database management

## 📁 Project Structure

```
├── client/                 # Frontend React application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   │   ├── admin/      # Admin panel components
│   │   │   ├── layout/     # Layout components
│   │   │   ├── product/    # Product-related components
│   │   │   └── ui/         # Base UI components (Shadcn/ui)
│   │   ├── hooks/          # Custom React hooks
│   │   ├── lib/            # Utility functions and configurations
│   │   ├── pages/          # Page components
│   │   └── main.tsx        # Application entry point
│   └── index.html          # HTML template
├── server/                 # Backend Express application
│   ├── uploads/            # File upload storage
│   ├── supabase.ts        # Supabase client configuration
│   ├── index.ts           # Server entry point
│   ├── routes.ts          # API routes
│   └── vite.ts            # Vite integration
├── shared/                 # Shared TypeScript schemas
│   └── schema.ts          # Zod validation schemas
├── docs/                   # Project documentation
│   └── project-requirements.txt
├── package.json           # Dependencies and scripts
├── tailwind.config.ts     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
└── vite.config.ts         # Vite configuration
```

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v18 or higher) - **Updated dependencies require Node.js 18+**
- **npm** or **yarn**
- **Supabase account** and project (free tier available)
- **Environment variables** configured (see .env.example)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd horizon
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Copy the example environment file and configure it:
   ```bash
   cp .env.example .env
   ```

   Edit the `.env` file with your actual values:
   ```env
   # Supabase Configuration
   SUPABASE_URL="https://your-project-id.supabase.co"
   SUPABASE_ANON_KEY="your-supabase-anon-key"

   # Authentication
   JWT_SECRET="your-super-secret-jwt-key"

   # Server
   PORT=3000
   NODE_ENV=development

   # Session
   SESSION_SECRET="your-session-secret"
   ```

4. **Supabase Setup**
   - Create a new project at [supabase.com](https://supabase.com)
   - Copy your project URL and anon key to the `.env` file
   - The database tables will be created automatically when you first run the application
   - Sample categories are pre-populated for testing

### Development

1. **Start the development server**
   ```bash
   npm run dev
   ```

2. **Open your browser**
   Navigate to `http://localhost:3000`

3. **Admin Access**
   - Create an admin account through the API or seed data
   - Access admin panel at `/admin`

### Production Build

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

## 📝 Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run check` - Type check with TypeScript

## 🗄️ Database Schema

### Products
- UUID primary key
- Multilingual names and descriptions (English/Arabic)
- Category relationships
- Image arrays (JSONB)
- Specifications and applications
- Timestamps

### Categories
- Hierarchical structure
- Multilingual support
- Product relationships

### Users
- Admin authentication
- Role-based access control

### Site Settings
- Configurable site-wide settings
- Multilingual content management

## 🔐 Authentication

The application uses JWT-based authentication for admin access:
- Secure password hashing with bcrypt
- User data stored securely in Supabase
- Protected admin routes and API endpoints
- Ready for Supabase Auth integration (future enhancement)

## 🌍 Internationalization

Full bilingual support with:
- English and Arabic language options
- RTL (Right-to-Left) layout for Arabic
- Localized content in Supabase database
- Dynamic language switching

## 📸 Image Management

- Local file storage with organized directory structure
- Multiple images per product
- Image validation and optimization
- Fallback to placeholder images

## 🎨 Design System

Custom luxury theme with:
- Sophisticated color palette
- Consistent typography
- Responsive breakpoints
- Accessible UI components
- Modern animations and transitions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## � Recent Updates

### Latest Dependency Updates (January 2025)
- **All npm packages updated** to their latest compatible versions
- **Port standardized** to 3000 for better development experience
- **TypeScript compatibility** ensured with updated type definitions
- **Cross-platform scripts** added using cross-env for Windows/Mac/Linux compatibility
- **Environment configuration** improved with comprehensive .env.example
- **Build process** optimized and verified working

### Database Migration to Supabase (January 2025)
- **Migrated from PostgreSQL/Drizzle ORM to Supabase**
- **Improved scalability** with Supabase's managed PostgreSQL
- **Enhanced type safety** with Supabase TypeScript integration
- **Real-time capabilities** ready for future features
- **Simplified deployment** with managed database service
- **Better development experience** with Supabase Studio

### Breaking Changes Addressed
- Updated React Select component props for null value handling
- Fixed Express.js route type definitions for newer @types/express
- Migrated database schema from Drizzle ORM to Supabase
- Enhanced API routes with Supabase client integration

## �📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation in `/docs`
- Review the project requirements
- Contact the development team

---

**Built with ❤️ for luxury stone and ceramic businesses**
