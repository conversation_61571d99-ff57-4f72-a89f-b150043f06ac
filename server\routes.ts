import type { Express, Request, Response, NextFunction } from "express";
import express from "express";
import { createServer, type Server } from "http";
import { supabase } from "./supabase";
import { insertCategorySchema, insertProductSchema, insertSiteSettingSchema } from "@shared/schema";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import multer from "multer";
import path from "path";
import fs from "fs";

// Extend Request type for multer
interface AuthRequest extends Request {
  user?: any;
  file?: Express.Multer.File;
  files?: Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };
}

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

// Configure multer for file uploads
const uploadDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const storage_multer = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage_multer,
  fileFilter: (req: any, file: any, cb: any) => {
    const allowedTypes = /jpeg|jpg|png|webp|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

// Authentication middleware
const authenticateToken = (req: AuthRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err: any, user: any) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Serve uploaded files
  app.use('/uploads', express.static(uploadDir));

  // Auth routes
  app.post('/api/auth/login', async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        return res.status(400).json({ message: 'Username and password required' });
      }

      const { data: user, error } = await supabase
        .from('users')
        .select('*')
        .eq('username', username)
        .single();

      if (error || !user) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      const validPassword = await bcrypt.compare(password, user.password);
      if (!validPassword) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      res.json({
        token,
        user: {
          id: user.id,
          username: user.username,
          role: user.role
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/auth/register', async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        return res.status(400).json({ message: 'Username and password required' });
      }

      // Check if user already exists
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('username', username)
        .single();

      if (existingUser) {
        return res.status(400).json({ message: 'Username already exists' });
      }

      const hashedPassword = await bcrypt.hash(password, 10);

      const { data: user, error } = await supabase
        .from('users')
        .insert({
          username,
          password: hashedPassword,
          role: 'admin'
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      res.status(201).json({
        token,
        user: {
          id: user.id,
          username: user.username,
          role: user.role
        }
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Categories routes
  app.get('/api/categories', async (req, res) => {
    try {
      const { data: categories, error } = await supabase
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;
      res.json(categories || []);
    } catch (error) {
      console.error('Get categories error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/categories/:id', async (req, res) => {
    try {
      const { data: category, error } = await supabase
        .from('categories')
        .select('*')
        .eq('id', req.params.id)
        .single();

      if (error || !category) {
        return res.status(404).json({ message: 'Category not found' });
      }
      res.json(category);
    } catch (error) {
      console.error('Get category error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/categories', authenticateToken, upload.single('image'), async (req: AuthRequest, res: Response) => {
    try {
      const categoryData = insertCategorySchema.parse({
        ...req.body,
        image: req.file ? `/uploads/${req.file.filename}` : undefined
      });

      const { data: category, error } = await supabase
        .from('categories')
        .insert(categoryData)
        .select()
        .single();

      if (error) throw error;
      res.status(201).json(category);
    } catch (error) {
      console.error('Create category error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.put('/api/categories/:id', authenticateToken, upload.single('image'), async (req: AuthRequest, res: Response) => {
    try {
      const categoryData = {
        ...req.body,
        image: req.file ? `/uploads/${req.file.filename}` : req.body.image,
        updated_at: new Date().toISOString()
      };

      const { data: category, error } = await supabase
        .from('categories')
        .update(categoryData)
        .eq('id', req.params.id)
        .select()
        .single();

      if (error) throw error;
      res.json(category);
    } catch (error) {
      console.error('Update category error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/categories/:id', authenticateToken, async (req, res) => {
    try {
      const { error } = await supabase
        .from('categories')
        .delete()
        .eq('id', req.params.id);

      if (error) throw error;
      res.status(204).send();
    } catch (error) {
      console.error('Delete category error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Products routes
  app.get('/api/products', async (req, res) => {
    try {
      let query = supabase
        .from('products')
        .select('*, categories(name, name_ar, slug)', { count: 'exact' })
        .eq('is_active', true);

      // Apply filters
      if (req.query.categoryId) {
        query = query.eq('category_id', req.query.categoryId as string);
      }

      if (req.query.search) {
        const searchTerm = req.query.search as string;
        query = query.or(`name.ilike.%${searchTerm}%,name_ar.ilike.%${searchTerm}%,code.ilike.%${searchTerm}%`);
      }

      if (req.query.isFeatured === 'true') {
        query = query.eq('is_featured', true);
      }

      // Apply pagination
      if (req.query.limit) {
        query = query.limit(parseInt(req.query.limit as string));
      }

      if (req.query.offset) {
        query = query.range(
          parseInt(req.query.offset as string),
          parseInt(req.query.offset as string) + (parseInt(req.query.limit as string) || 10) - 1
        );
      }

      // Order by sort_order and created_at
      query = query.order('sort_order', { ascending: true }).order('created_at', { ascending: false });

      const { data: products, error, count } = await query;

      if (error) throw error;
      res.json({ products: products || [], total: count || 0 });
    } catch (error) {
      console.error('Get products error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/products/:id', async (req, res) => {
    try {
      const { data: product, error } = await supabase
        .from('products')
        .select('*, categories(name, name_ar, slug)')
        .eq('id', req.params.id)
        .single();

      if (error || !product) {
        return res.status(404).json({ message: 'Product not found' });
      }
      res.json(product);
    } catch (error) {
      console.error('Get product error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/products', authenticateToken, upload.array('images', 10), async (req: AuthRequest, res: Response) => {
    try {
      const images = req.files ? (req.files as Express.Multer.File[]).map(file => `/uploads/${file.filename}`) : [];

      const productData = insertProductSchema.parse({
        ...req.body,
        images,
        specifications: req.body.specifications ? JSON.parse(req.body.specifications) : {},
        specifications_ar: req.body.specificationsAr ? JSON.parse(req.body.specificationsAr) : {},
        applications: req.body.applications ? JSON.parse(req.body.applications) : [],
        applications_ar: req.body.applicationsAr ? JSON.parse(req.body.applicationsAr) : [],
      });

      const { data: product, error } = await supabase
        .from('products')
        .insert(productData)
        .select()
        .single();

      if (error) throw error;
      res.status(201).json(product);
    } catch (error) {
      console.error('Create product error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.put('/api/products/:id', authenticateToken, upload.array('images', 10), async (req: AuthRequest, res: Response) => {
    try {
      const newImages = req.files ? (req.files as Express.Multer.File[]).map(file => `/uploads/${file.filename}`) : [];
      const existingImages = req.body.existingImages ? JSON.parse(req.body.existingImages) : [];
      const images = [...existingImages, ...newImages];

      const productData = {
        ...req.body,
        images,
        specifications: req.body.specifications ? JSON.parse(req.body.specifications) : undefined,
        specifications_ar: req.body.specificationsAr ? JSON.parse(req.body.specificationsAr) : undefined,
        applications: req.body.applications ? JSON.parse(req.body.applications) : undefined,
        applications_ar: req.body.applicationsAr ? JSON.parse(req.body.applicationsAr) : undefined,
        updated_at: new Date().toISOString()
      };

      const { data: product, error } = await supabase
        .from('products')
        .update(productData)
        .eq('id', req.params.id)
        .select()
        .single();

      if (error) throw error;
      res.json(product);
    } catch (error) {
      console.error('Update product error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/products/:id', authenticateToken, async (req, res) => {
    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', req.params.id);

      if (error) throw error;
      res.status(204).send();
    } catch (error) {
      console.error('Delete product error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Site settings routes
  app.get('/api/settings', async (req, res) => {
    try {
      const { data: settings, error } = await supabase
        .from('site_settings')
        .select('*');

      if (error) throw error;

      // Convert array to object for easier access
      const settingsObj = (settings || []).reduce((acc: any, setting: any) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {});

      res.json(settingsObj);
    } catch (error) {
      console.error('Get settings error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/settings', authenticateToken, async (req, res) => {
    try {
      const settingData = insertSiteSettingSchema.parse(req.body);

      const { data: setting, error } = await supabase
        .from('site_settings')
        .upsert(settingData, { onConflict: 'key' })
        .select()
        .single();

      if (error) throw error;
      res.status(201).json(setting);
    } catch (error) {
      console.error('Create setting error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.put('/api/settings/:key', authenticateToken, async (req, res) => {
    try {
      const { data: setting, error } = await supabase
        .from('site_settings')
        .update({
          value: req.body.value,
          updated_at: new Date().toISOString()
        })
        .eq('key', req.params.key)
        .select()
        .single();

      if (error) throw error;
      res.json(setting);
    } catch (error) {
      console.error('Update setting error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
