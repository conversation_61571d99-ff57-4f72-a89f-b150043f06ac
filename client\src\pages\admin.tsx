import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ProductForm } from "@/components/admin/product-form";
import { CategoryForm } from "@/components/admin/category-form";
import { useLanguage } from "@/hooks/use-language";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Product, Category } from "@shared/schema";
import { 
  LayoutDashboard, 
  Package, 
  Tags, 
  Settings, 
  Images, 
  Plus, 
  Edit, 
  Trash2, 
  Eye,
  Users,
  TrendingUp,
  MessageSquare,
  AlertCircle
} from "lucide-react";

export default function Admin() {
  const [activeTab, setActiveTab] = useState("dashboard");
  const [isProductFormOpen, setIsProductFormOpen] = useState(false);
  const [isCategoryFormOpen, setIsCategoryFormOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | undefined>();
  const [editingCategory, setEditingCategory] = useState<Category | undefined>();
  const [productSearch, setProductSearch] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const { t, language } = useLanguage();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Check if user is authenticated (simplified - in real app would use proper auth)
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    return localStorage.getItem('isAdminAuthenticated') === 'true';
  });
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });

  const { data: products, isLoading: productsLoading } = useQuery({
    queryKey: ['/api/products', { search: productSearch, categoryId: categoryFilter }],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (productSearch) params.append('search', productSearch);
      if (categoryFilter) params.append('categoryId', categoryFilter);
      
      const response = await fetch(`/api/products?${params.toString()}`);
      return response.json() as Promise<{ products: Product[]; total: number }>;
    },
    enabled: isAuthenticated,
  });

  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['/api/categories'],
    queryFn: async () => {
      const response = await fetch('/api/categories');
      return response.json() as Promise<Category[]>;
    },
    enabled: isAuthenticated,
  });

  const deleteProductMutation = useMutation({
    mutationFn: async (productId: string) => {
      return apiRequest('DELETE', `/api/products/${productId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      toast({
        title: t('common.success'),
        description: "Product deleted successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: t('common.error'),
        description: error.message || "Failed to delete product",
        variant: "destructive",
      });
    },
  });

  const deleteCategoryMutation = useMutation({
    mutationFn: async (categoryId: string) => {
      return apiRequest('DELETE', `/api/categories/${categoryId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      toast({
        title: t('common.success'),
        description: "Category deleted successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: t('common.error'),
        description: error.message || "Failed to delete category",
        variant: "destructive",
      });
    },
  });

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Simple demo authentication - in real app would use proper JWT auth
    if (loginForm.username === 'admin' && loginForm.password === 'admin') {
      localStorage.setItem('isAdminAuthenticated', 'true');
      setIsAuthenticated(true);
      toast({
        title: t('common.success'),
        description: "Login successful!",
      });
    } else {
      toast({
        title: t('common.error'),
        description: "Invalid credentials. Use admin/admin for demo.",
        variant: "destructive",
      });
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('isAdminAuthenticated');
    setIsAuthenticated(false);
    toast({
      title: t('common.success'),
      description: "Logged out successfully!",
    });
  };

  const handleDeleteProduct = async (productId: string) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      deleteProductMutation.mutate(productId);
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      deleteCategoryMutation.mutate(categoryId);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center bg-warm-white">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="w-16 h-16 gold-gradient rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="font-playfair text-2xl text-charcoal">
              {t('admin.title')}
            </CardTitle>
            <p className="text-medium-gray">{t('admin.subtitle')}</p>
          </CardHeader>
          <CardContent>
            <Alert className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Demo credentials: <strong>admin / admin</strong>
              </AlertDescription>
            </Alert>
            
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Username
                </label>
                <Input
                  type="text"
                  value={loginForm.username}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, username: e.target.value }))}
                  placeholder="Enter username"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Password
                </label>
                <Input
                  type="password"
                  value={loginForm.password}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="Enter password"
                  required
                />
              </div>
              <Button 
                type="submit" 
                className="w-full gold-gradient text-white hover:shadow-lg transition-all duration-300"
              >
                Login
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  const stats = {
    totalProducts: products?.total || 0,
    totalCategories: categories?.length || 0,
    monthlyViews: 2450,
    quoteRequests: 32,
  };

  return (
    <div className="min-h-screen pt-20 bg-warm-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="font-playfair text-4xl font-bold text-charcoal">
              {t('admin.title')}
            </h1>
            <p className="text-medium-gray mt-2">{t('admin.subtitle')}</p>
          </div>
          <Button
            onClick={handleLogout}
            variant="outline"
            className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white"
          >
            {t('admin.logout')}
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid grid-cols-5 w-full max-w-2xl">
            <TabsTrigger value="dashboard" className="flex items-center space-x-2">
              <LayoutDashboard className="h-4 w-4" />
              <span className="hidden sm:inline">{t('admin.dashboard')}</span>
            </TabsTrigger>
            <TabsTrigger value="products" className="flex items-center space-x-2">
              <Package className="h-4 w-4" />
              <span className="hidden sm:inline">{t('admin.products')}</span>
            </TabsTrigger>
            <TabsTrigger value="categories" className="flex items-center space-x-2">
              <Tags className="h-4 w-4" />
              <span className="hidden sm:inline">{t('admin.categories')}</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center space-x-2">
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">{t('admin.settings')}</span>
            </TabsTrigger>
            <TabsTrigger value="media" className="flex items-center space-x-2">
              <Images className="h-4 w-4" />
              <span className="hidden sm:inline">{t('admin.media')}</span>
            </TabsTrigger>
          </TabsList>

          {/* Dashboard Tab */}
          <TabsContent value="dashboard" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">{t('admin.totalProducts')}</p>
                      <p className="text-3xl font-bold">{stats.totalProducts}</p>
                    </div>
                    <Package className="h-8 w-8 text-blue-200" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">{t('admin.categories')}</p>
                      <p className="text-3xl font-bold">{stats.totalCategories}</p>
                    </div>
                    <Tags className="h-8 w-8 text-green-200" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm">{t('admin.monthlyViews')}</p>
                      <p className="text-3xl font-bold">{stats.monthlyViews.toLocaleString()}</p>
                    </div>
                    <Eye className="h-8 w-8 text-purple-200" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-100 text-sm">{t('admin.quoteRequests')}</p>
                      <p className="text-3xl font-bold">{stats.quoteRequests}</p>
                    </div>
                    <MessageSquare className="h-8 w-8 text-orange-200" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="font-playfair text-xl text-charcoal">
                  {t('admin.recentActivity')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4 p-4 hover:bg-beige rounded-lg transition-colors duration-200">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <Plus className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-charcoal font-medium">New product added: Sample Product</p>
                      <p className="text-sm text-medium-gray">2 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 p-4 hover:bg-beige rounded-lg transition-colors duration-200">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Edit className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-charcoal font-medium">Category updated</p>
                      <p className="text-sm text-medium-gray">5 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 p-4 hover:bg-beige rounded-lg transition-colors duration-200">
                    <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                      <MessageSquare className="h-5 w-5 text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-charcoal font-medium">Quote request received</p>
                      <p className="text-sm text-medium-gray">1 day ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Products Tab */}
          <TabsContent value="products" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="font-playfair text-2xl font-bold text-charcoal">
                  Product Management
                </h2>
                <p className="text-medium-gray">Manage your stone and ceramic products</p>
              </div>
              <Button
                onClick={() => {
                  setEditingProduct(undefined);
                  setIsProductFormOpen(true);
                }}
                className="gold-gradient text-white hover:shadow-lg transition-all duration-300"
              >
                <Plus className="h-4 w-4 mr-2" />
                {t('admin.addNewProduct')}
              </Button>
            </div>

            {/* Filters */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="Search products..."
                      value={productSearch}
                      onChange={(e) => setProductSearch(e.target.value)}
                      className="border-beige focus:border-gold"
                    />
                  </div>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-full md:w-48 border-beige focus:border-gold">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Categories</SelectItem>
                      {categories?.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {language === 'ar' && category.nameAr ? category.nameAr : category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Products Table */}
            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-beige">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-medium text-charcoal uppercase tracking-wider">
                          Product
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-charcoal uppercase tracking-wider">
                          Code
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-charcoal uppercase tracking-wider">
                          Category
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-charcoal uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-charcoal uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-beige">
                      {productsLoading ? (
                        [...Array(5)].map((_, i) => (
                          <tr key={i}>
                            <td className="px-6 py-4">
                              <div className="flex items-center space-x-4">
                                <Skeleton className="w-12 h-12 rounded-lg" />
                                <div className="space-y-2">
                                  <Skeleton className="h-4 w-32" />
                                  <Skeleton className="h-3 w-24" />
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4"><Skeleton className="h-4 w-16" /></td>
                            <td className="px-6 py-4"><Skeleton className="h-4 w-20" /></td>
                            <td className="px-6 py-4"><Skeleton className="h-4 w-16" /></td>
                            <td className="px-6 py-4"><Skeleton className="h-4 w-16" /></td>
                          </tr>
                        ))
                      ) : products?.products.length === 0 ? (
                        <tr>
                          <td colSpan={5} className="px-6 py-12 text-center">
                            <div className="text-medium-gray">
                              <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                              <p className="text-lg font-medium">No products found</p>
                              <p className="text-sm">Start by adding your first product</p>
                            </div>
                          </td>
                        </tr>
                      ) : (
                        products?.products.map((product) => {
                          const productName = language === 'ar' && product.nameAr ? product.nameAr : product.name;
                          const productDescription = language === 'ar' && product.descriptionAr ? product.descriptionAr : product.description;
                          const category = categories?.find(c => c.id === product.categoryId);
                          const categoryName = category 
                            ? (language === 'ar' && category.nameAr ? category.nameAr : category.name)
                            : '';

                          return (
                            <tr key={product.id} className="hover:bg-warm-white transition-colors duration-200">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <img
                                    src={product.images?.[0] || 'https://images.unsplash.com/photo-1565043666747-69f6646db940?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=60&h=60'}
                                    alt={productName}
                                    className="w-12 h-12 rounded-lg object-cover mr-4"
                                  />
                                  <div>
                                    <div className="text-sm font-medium text-charcoal">{productName}</div>
                                    <div className="text-sm text-medium-gray line-clamp-1">{productDescription}</div>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-charcoal">
                                {product.code}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                                  {categoryName}
                                </Badge>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <Badge 
                                  variant={product.is_active ? "default" : "secondary"}
                                  className={product.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}
                                >
                                  {product.is_active ? 'Active' : 'Inactive'}
                                </Badge>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setEditingProduct(product);
                                    setIsProductFormOpen(true);
                                  }}
                                  className="text-gold hover:text-bronze hover:bg-gold/10"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => product.id && handleDeleteProduct(product.id)}
                                  className="text-red-600 hover:text-red-800 hover:bg-red-50"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </td>
                            </tr>
                          );
                        })
                      )}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="font-playfair text-2xl font-bold text-charcoal">
                  Category Management
                </h2>
                <p className="text-medium-gray">Organize your product categories</p>
              </div>
              <Button
                onClick={() => {
                  setEditingCategory(undefined);
                  setIsCategoryFormOpen(true);
                }}
                className="gold-gradient text-white hover:shadow-lg transition-all duration-300"
              >
                <Plus className="h-4 w-4 mr-2" />
                {t('admin.addNewCategory')}
              </Button>
            </div>

            {categoriesLoading ? (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <Skeleton className="h-32 w-full mb-4 rounded-lg" />
                      <Skeleton className="h-4 w-3/4 mb-2" />
                      <Skeleton className="h-3 w-1/2" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : categories?.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <Tags className="h-12 w-12 mx-auto mb-4 text-medium-gray opacity-50" />
                  <h3 className="text-lg font-medium text-charcoal mb-2">No categories found</h3>
                  <p className="text-medium-gray">Start by creating your first category</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {categories?.map((category) => {
                  const categoryName = language === 'ar' && category.name_ar ? category.name_ar : category.name;
                  const categoryDesc = language === 'ar' && category.description_ar ? category.description_ar : category.description;

                  return (
                    <Card key={category.id} className="group hover:shadow-lg transition-all duration-300">
                      <CardContent className="p-6">
                        {category.image && (
                          <img
                            src={category.image}
                            alt={categoryName}
                            className="w-full h-32 object-cover rounded-lg mb-4"
                          />
                        )}
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-playfair text-lg font-semibold text-charcoal">
                            {categoryName}
                          </h3>
                          <Badge 
                            variant={category.is_active ? "default" : "secondary"}
                            className={category.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}
                          >
                            {category.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        <p className="text-medium-gray text-sm mb-4 line-clamp-2">
                          {categoryDesc}
                        </p>
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-medium-gray">
                            Sort: {category.sort_order}
                          </span>
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setEditingCategory(category);
                                setIsCategoryFormOpen(true);
                              }}
                              className="text-gold hover:text-bronze hover:bg-gold/10"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => category.id && handleDeleteCategory(category.id)}
                              className="text-red-600 hover:text-red-800 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="font-playfair text-2xl text-charcoal">
                  {t('admin.settings')}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <Settings className="h-12 w-12 mx-auto mb-4 text-medium-gray opacity-50" />
                  <h3 className="text-lg font-medium text-charcoal mb-2">Settings Coming Soon</h3>
                  <p className="text-medium-gray">Site settings management will be available here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Media Tab */}
          <TabsContent value="media" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="font-playfair text-2xl text-charcoal">
                  {t('admin.media')}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <Images className="h-12 w-12 mx-auto mb-4 text-medium-gray opacity-50" />
                  <h3 className="text-lg font-medium text-charcoal mb-2">Media Library Coming Soon</h3>
                  <p className="text-medium-gray">Manage your uploaded images and media files</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Product Form Dialog */}
      <Dialog open={isProductFormOpen} onOpenChange={setIsProductFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="font-playfair text-2xl">
              {editingProduct ? 'Edit Product' : t('admin.addNewProduct')}
            </DialogTitle>
          </DialogHeader>
          <ProductForm
            product={editingProduct}
            onSuccess={() => {
              setIsProductFormOpen(false);
              setEditingProduct(undefined);
            }}
            onCancel={() => {
              setIsProductFormOpen(false);
              setEditingProduct(undefined);
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Category Form Dialog */}
      <Dialog open={isCategoryFormOpen} onOpenChange={setIsCategoryFormOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="font-playfair text-2xl">
              {editingCategory ? 'Edit Category' : t('admin.addNewCategory')}
            </DialogTitle>
          </DialogHeader>
          <CategoryForm
            category={editingCategory}
            onSuccess={() => {
              setIsCategoryFormOpen(false);
              setEditingCategory(undefined);
            }}
            onCancel={() => {
              setIsCategoryFormOpen(false);
              setEditingCategory(undefined);
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
